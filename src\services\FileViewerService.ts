// Generic file viewer service that can handle multiple file types
export interface FileViewerState {
  isOpen: boolean
  filePath: string | null
  fileName: string | null
  fileType: string | null
  zoom: number
  isLoading: boolean
  error: string | null
}

class FileViewerService {
  private state: FileViewerState = {
    isOpen: false,
    filePath: null,
    fileName: null,
    fileType: null,
    zoom: 100,
    isLoading: false,
    error: null
  }

  private subscribers: Array<(state: FileViewerState) => void> = []

  subscribe(callback: (state: FileViewerState) => void): () => void {
    this.subscribers.push(callback)
    return () => {
      this.subscribers = this.subscribers.filter(sub => sub !== callback)
    }
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.state))
  }

  getState(): FileViewerState {
    return { ...this.state }
  }

  openFile(filePath: string, fileName: string, fileType?: string) {
    console.log('FileViewerService: Opening file:', { filePath, fileName, fileType })
    this.state = {
      ...this.state,
      isOpen: true,
      filePath,
      fileName,
      fileType: fileType || this.detectFileType(fileName),
      isLoading: true,
      error: null
    }
    this.notifySubscribers()
  }

  closeFile() {
    console.log('FileViewerService: Closing file')
    this.state = {
      ...this.state,
      isOpen: false,
      filePath: null,
      fileName: null,
      fileType: null,
      isLoading: false,
      error: null
    }
    this.notifySubscribers()
  }

  setZoom(zoom: number) {
    this.state = {
      ...this.state,
      zoom: Math.max(25, Math.min(400, zoom))
    }
    this.notifySubscribers()
  }

  setLoading(isLoading: boolean) {
    this.state = {
      ...this.state,
      isLoading
    }
    this.notifySubscribers()
  }

  setError(error: string | null) {
    this.state = {
      ...this.state,
      error,
      isLoading: false
    }
    this.notifySubscribers()
  }

  private detectFileType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase() || ''
    
    if (extension === 'pdf') return 'pdf'
    if (['md', 'markdown'].includes(extension)) return 'markdown'
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp', 'ico'].includes(extension)) return 'image'
    if (['txt', 'log', 'csv', 'xml', 'json', 'yaml', 'yml'].includes(extension)) return 'text'
    if (['js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'].includes(extension)) return 'code'
    
    return 'unknown'
  }
}

export const fileViewerService = new FileViewerService()
