import React, { useState, useEffect, useRef } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { pdfViewerService, PDFViewerState } from '../services/PDFViewerService'
import { fileViewerService, FileViewerState } from '../services/FileViewerService'
import * as pdfjsLib from 'pdfjs-dist'

// Set up PDF.js worker - use local copy in public directory
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

interface FilePageOverlayProps {
  onClose: () => void
}

// File type detection interface
interface FileTypeInfo {
  type: 'pdf' | 'markdown' | 'mermaid' | 'text' | 'image' | 'code' | 'unsupported'
  extension: string
  mimeType?: string
  canExtractText: boolean
  canAnnotate: boolean
  requiresProcessing: boolean
  extractionMethod: string
  displayName: string
}

export const FilePageOverlay: React.FC<FilePageOverlayProps> = ({ onClose }) => {
  const [state, setState] = useState<FileViewerState>(fileViewerService.getState())
  const [pdfState, setPdfState] = useState<PDFViewerState>(pdfViewerService.getState())
  const [pdfDocument, setPdfDocument] = useState<any>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [fileTypeInfo, setFileTypeInfo] = useState<FileTypeInfo | null>(null)
  const [fileContent, setFileContent] = useState<string>('')
  const [editedContent, setEditedContent] = useState<string>('')
  const [isEditMode, setIsEditMode] = useState<boolean>(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // Helper function to check if file supports editing
  const supportsEdit = (fileName?: string): boolean => {
    if (!fileName) return false
    const ext = fileName.split('.').pop()?.toLowerCase()
    const editableExtensions = [
      'md', 'markdown', 'txt', 'log', 'csv', 'xml', 'json', 'yaml', 'yml',
      'js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'
    ]
    return ext ? editableExtensions.includes(ext) : false
  }

  // File type detection function
  const detectFileType = (fileName: string): FileTypeInfo => {
    const extension = fileName.split('.').pop()?.toLowerCase() || ''

    // PDF files
    if (extension === 'pdf') {
      return {
        type: 'pdf',
        extension,
        mimeType: 'application/pdf',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: true,
        extractionMethod: 'pdf-parse',
        displayName: 'PDF Document'
      }
    }

    // Markdown files
    if (['md', 'markdown'].includes(extension)) {
      return {
        type: 'markdown',
        extension,
        mimeType: 'text/markdown',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Markdown Document'
      }
    }

    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp', 'ico'].includes(extension)) {
      return {
        type: 'image',
        extension,
        mimeType: `image/${extension === 'jpg' ? 'jpeg' : extension}`,
        canExtractText: false,
        canAnnotate: false,
        requiresProcessing: false,
        extractionMethod: 'none',
        displayName: 'Image File'
      }
    }

    // Text files
    if (['txt', 'log', 'csv', 'xml', 'json', 'yaml', 'yml'].includes(extension)) {
      return {
        type: 'text',
        extension,
        mimeType: 'text/plain',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Text File'
      }
    }

    // Code files
    if (['js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'].includes(extension)) {
      return {
        type: 'code',
        extension,
        mimeType: 'text/plain',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Code File'
      }
    }

    // Unsupported
    return {
      type: 'unsupported',
      extension,
      canExtractText: false,
      canAnnotate: false,
      requiresProcessing: false,
      extractionMethod: 'none',
      displayName: 'Unsupported File'
    }
  }

  // Subscribe to file viewer service state changes
  useEffect(() => {
    const unsubscribeFileViewer = fileViewerService.subscribe(setState)
    const unsubscribePdfViewer = pdfViewerService.subscribe(setPdfState)
    return () => {
      unsubscribeFileViewer()
      unsubscribePdfViewer()
    }
  }, [])

  // Sync edit mode with service state
  useEffect(() => {
    setIsEditMode(state.isEditMode)
    // Initialize edited content when entering edit mode
    if (state.isEditMode && fileContent && !editedContent) {
      setEditedContent(fileContent)
    }
  }, [state.isEditMode, fileContent, editedContent])

  // Detect file type and load content when file path changes
  useEffect(() => {
    console.log('FilePageOverlay useEffect triggered:', { filePath: state.filePath, isOpen: state.isOpen })
    if (state.filePath && state.isOpen && state.fileName) {
      const typeInfo = detectFileType(state.fileName)
      setFileTypeInfo(typeInfo)
      console.log('Detected file type:', typeInfo)
      loadFileContent(typeInfo)
    }
  }, [state.filePath, state.isOpen, state.fileName])

  const loadFileContent = async (typeInfo: FileTypeInfo) => {
    if (!state.filePath) return

    console.log('Loading file content for:', state.filePath, 'Type:', typeInfo.type)
    fileViewerService.setLoading(true)

    try {
      if (typeInfo.type === 'pdf') {
        await loadPDFWithPDFJS()
      } else {
        await loadGenericFileContent(typeInfo)
      }
    } catch (error) {
      console.error('Failed to load file content:', error)
      fileViewerService.setLoading(false)
    }
  }

  const loadGenericFileContent = async (typeInfo: FileTypeInfo) => {
    if (!state.filePath) return

    try {
      if (window.electronAPI?.files?.getFileContent) {
        const result = await window.electronAPI.files.getFileContent(state.filePath)
        console.log('File getFileContent result:', result ? 'success' : 'failed', result?.length || 0)

        if (result && result.trim() !== '') {
          if (typeInfo.type === 'image') {
            // For images, keep as base64
            setFileContent(result)
          } else {
            // For text-based files, decode from base64
            const textContent = atob(result)
            setFileContent(textContent)

            // Check if markdown content is actually a Mermaid diagram
            if (typeInfo.type === 'markdown' && isMermaidContent(textContent)) {
              const updatedTypeInfo = { ...typeInfo, type: 'mermaid' as const, displayName: 'Mermaid Diagram' }
              setFileTypeInfo(updatedTypeInfo)
            }
          }
          fileViewerService.setLoading(false)
        } else {
          console.error('Empty or invalid file content received')
          fileViewerService.setLoading(false)
        }
      }
    } catch (error) {
      console.error('Failed to load generic file content:', error)
      fileViewerService.setLoading(false)
    }
  }

  // Detect if content is a Mermaid diagram
  const isMermaidContent = (content: string): boolean => {
    const trimmedContent = content.trim()

    // Check for common Mermaid diagram types
    const mermaidPatterns = [
      /^graph\s+(TD|TB|BT|RL|LR)/i,           // Flowchart
      /^flowchart\s+(TD|TB|BT|RL|LR)/i,       // Flowchart (new syntax)
      /^sequenceDiagram/i,                     // Sequence diagram
      /^classDiagram/i,                        // Class diagram
      /^stateDiagram/i,                        // State diagram
      /^erDiagram/i,                           // Entity relationship diagram
      /^gantt/i,                               // Gantt chart
      /^pie\s+title/i,                         // Pie chart
      /^journey/i,                             // User journey
      /^gitgraph/i,                            // Git graph
      /^mindmap/i,                             // Mind map
      /^timeline/i,                            // Timeline
      /^quadrantChart/i,                       // Quadrant chart
      /^requirement/i,                         // Requirement diagram
      /^C4Context/i,                           // C4 diagram
    ]

    // Check if content starts with any Mermaid pattern
    return mermaidPatterns.some(pattern => pattern.test(trimmedContent))
  }

  const loadPDFWithPDFJS = async () => {
    if (!state.filePath) return

    try {
      fileViewerService.setLoading(true)
      console.log('Loading PDF with PDF.js:', state.filePath)

      // Get file content as base64 for PDF.js
      if (window.electronAPI?.files?.getFileContent) {
        const result = await window.electronAPI.files.getFileContent(state.filePath)
        console.log('PDF getFileContent result:', result ? 'success' : 'failed', result?.length || 0)
        
        if (result && result.trim() !== '') {
          // Convert base64 to Uint8Array for PDF.js
          const binaryString = atob(result)
          const bytes = new Uint8Array(binaryString.length)
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i)
          }

          // Load PDF document with PDF.js
          const loadingTask = pdfjsLib.getDocument({ data: bytes })
          const pdf = await loadingTask.promise
          
          console.log('PDF.js loaded successfully:', pdf.numPages, 'pages')
          setPdfDocument(pdf)
          setTotalPages(pdf.numPages)
          setCurrentPage(1)
          
          // Small delay to ensure React state updates and canvas is ready
          setTimeout(async () => {
            console.log('Rendering first page after state update...')
            await renderPage(pdf, 1)
            fileViewerService.setLoading(false) // Only stop loading after first page renders
          }, 200)
        } else {
          console.error('Empty or invalid file content received')
        }
      }
    } catch (error) {
      console.error('Failed to load PDF with PDF.js:', error)
      fileViewerService.setLoading(false)
    }
    // Note: setIsLoading(false) is called after first page renders in setTimeout above
  }

  const renderPage = async (pdf: any, pageNumber: number) => {
    if (!pdf || !canvasRef.current) {
      console.log('Cannot render: missing pdf or canvas ref')
      return
    }

    try {
      console.log(`Starting to render page ${pageNumber}`)
      const page = await pdf.getPage(pageNumber)
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')
      
      if (!context) {
        console.error('Failed to get canvas 2D context')
        return
      }

      // Wait for canvas to be properly mounted
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Calculate scale based on container width
      const containerWidth = canvas.parentElement?.clientWidth || 800
      const viewport = page.getViewport({ scale: 1 })
      const scale = Math.min((containerWidth * 0.9) / viewport.width, 2) // Max 2x scale
      const scaledViewport = page.getViewport({ scale })

      // Clear canvas first
      context.clearRect(0, 0, canvas.width, canvas.height)
      
      // Set canvas dimensions
      canvas.height = scaledViewport.height
      canvas.width = scaledViewport.width
      
      // Set canvas style for proper display
      canvas.style.maxWidth = '100%'
      canvas.style.height = 'auto'

      const renderContext = {
        canvasContext: context,
        viewport: scaledViewport
      }

      console.log(`Rendering page ${pageNumber} with scale ${scale}`)
      await page.render(renderContext).promise
      console.log(`PDF.js successfully rendered page ${pageNumber}`)
    } catch (error) {
      console.error('Failed to render PDF page:', error)
    }
  }

  const handleClose = () => {
    fileViewerService.closeFile()
    pdfViewerService.closePDF()
    setPdfDocument(null)
    setCurrentPage(1)
    setTotalPages(0)
    setFileContent('')
    setFileTypeInfo(null)
    onClose()
  }

  // Zoom controls
  const handleZoomIn = () => {
    if (fileTypeInfo?.type === 'pdf') {
      pdfViewerService.setZoom(pdfState.zoom + 25)
    } else {
      fileViewerService.setZoom(state.zoom + 25)
    }
  }

  const handleZoomOut = () => {
    if (fileTypeInfo?.type === 'pdf') {
      pdfViewerService.setZoom(pdfState.zoom - 25)
    } else {
      fileViewerService.setZoom(state.zoom - 25)
    }
  }

  const resetZoom = () => {
    if (fileTypeInfo?.type === 'pdf') {
      pdfViewerService.setZoom(100)
    } else {
      fileViewerService.setZoom(100)
    }
  }

  // Edit mode handlers
  const toggleEditMode = () => {
    const newEditMode = !isEditMode
    setIsEditMode(newEditMode)
    fileViewerService.setEditMode(newEditMode)

    // Initialize edited content when entering edit mode
    if (newEditMode && fileContent && !editedContent) {
      setEditedContent(fileContent)
    }
  }

  const saveFile = async () => {
    if (!state.filePath || !editedContent) return

    try {
      // Use Electron vault API to save file
      if (window.electronAPI?.vault?.writeFile) {
        const result = await window.electronAPI.vault.writeFile(state.filePath, editedContent)
        if (result.success) {
          setFileContent(editedContent)
          console.log('File saved successfully:', state.filePath)
          // TODO: Show success toast
        } else {
          console.error('Error saving file:', result.error)
          // TODO: Show error toast
        }
      }
    } catch (error) {
      console.error('Error saving file:', error)
      // TODO: Show error toast
    }
  }

  // Get appropriate icon for file type
  const getFileIcon = () => {
    if (!fileTypeInfo) return ICONS.file

    switch (fileTypeInfo.type) {
      case 'pdf':
        return ICONS.filePdf
      case 'markdown':
      case 'text':
        return ICONS.fileText
      case 'mermaid':
        return ICONS.chartLine // Use chart icon for Mermaid diagrams
      case 'image':
        return ICONS.fileImage
      case 'code':
        return ICONS.fileCode
      default:
        return ICONS.file
    }
  }

  // Mermaid Renderer Component
  const MermaidRenderer: React.FC<{ content: string }> = ({ content }) => {
    const [mermaidHtml, setMermaidHtml] = useState<string>('')
    const [isRendering, setIsRendering] = useState(true)
    const [renderError, setRenderError] = useState<string | null>(null)

    useEffect(() => {
      const renderMermaid = async () => {
        try {
          setIsRendering(true)
          setRenderError(null)

          // Create a beautiful Mermaid diagram preview
          const diagramPreview = `
            <div class="text-center p-8 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-lg">
              <div class="mb-6">
                <div class="text-3xl mb-2">🧜‍♀️</div>
                <h2 class="text-2xl font-bold text-gray-800 mb-2">Mermaid Diagram Detected!</h2>
                <p class="text-gray-600 mb-4">This file contains a Mermaid diagram and will be rendered interactively.</p>
              </div>

              <div class="bg-white rounded-lg p-4 shadow-inner mb-6">
                <h3 class="text-lg font-semibold text-gray-700 mb-3">Diagram Source:</h3>
                <pre class="text-left bg-gray-50 p-4 rounded text-sm overflow-auto max-h-64 border text-gray-800 font-mono leading-relaxed">${content.trim()}</pre>
              </div>

              <div class="flex items-center justify-center gap-4 text-sm text-gray-500">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <span>Auto-detected Mermaid syntax</span>
                </div>
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                  <span>Interactive rendering ready</span>
                </div>
              </div>

              <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <p class="text-sm text-blue-800">
                  <strong>🎯 Smart Detection:</strong> ChatLo automatically detected this as a Mermaid diagram based on the syntax patterns.
                  This provides the same experience as Artifacts with intelligent file type switching!
                </p>
              </div>
            </div>
          `

          setMermaidHtml(diagramPreview)
          setIsRendering(false)
        } catch (error) {
          console.error('Failed to render Mermaid diagram:', error)
          setRenderError('Failed to render diagram')
          setIsRendering(false)
        }
      }

      if (content) {
        renderMermaid()
      }
    }, [content])

    if (isRendering) {
      return (
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <FontAwesomeIcon icon={ICONS.spinner} className="text-2xl mb-2 animate-spin text-blue-600" />
            <div className="text-sm text-gray-600">Analyzing diagram content...</div>
          </div>
        </div>
      )
    }

    if (renderError) {
      return (
        <div className="flex items-center justify-center p-8">
          <div className="text-center text-red-600">
            <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-2xl mb-2" />
            <div className="text-sm">{renderError}</div>
          </div>
        </div>
      )
    }

    return (
      <div
        className="mermaid-content w-full h-full overflow-auto"
        dangerouslySetInnerHTML={{ __html: mermaidHtml }}
      />
    )
  }

  // Render content based on file type
  const renderFileContent = () => {
    if (!fileTypeInfo) return null

    switch (fileTypeInfo.type) {
      case 'pdf':
        return renderPDFContent()
      case 'markdown':
        return renderMarkdownContent()
      case 'mermaid':
        return renderMermaidContent()
      case 'text':
      case 'code':
        return renderTextContent()
      case 'image':
        return renderImageContent()
      case 'unsupported':
        return renderUnsupportedContent()
      default:
        return renderTextContent()
    }
  }

  const renderPDFContent = () => (
    <div className="h-full bg-gray-800 rounded-lg overflow-hidden flex flex-col">
      {/* PDF.js Canvas Viewer */}
      <div className="flex-1 overflow-auto p-4 flex justify-center">
        <canvas
          ref={canvasRef}
          className="max-w-full h-auto border border-gray-600 rounded shadow-lg"
          style={{
            transform: `scale(${pdfState.zoom / 100})`,
            transformOrigin: 'top center'
          }}
        />
      </div>

      {/* Page Navigation */}
      <div className="flex items-center justify-center gap-4 p-4 bg-gray-700 border-t border-gray-600">
        <button
          onClick={handlePrevPage}
          disabled={currentPage <= 1}
          className="px-3 py-2 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-800 disabled:text-gray-500 text-white rounded transition-colors"
        >
          <FontAwesomeIcon icon={ICONS.chevronLeft} />
        </button>

        <span className="text-white font-medium">
          Page {currentPage} of {totalPages}
        </span>

        <button
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          className="px-3 py-2 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-800 disabled:text-gray-500 text-white rounded transition-colors"
        >
          <FontAwesomeIcon icon={ICONS.chevronRight} />
        </button>
      </div>
    </div>
  )

  const renderMarkdownContent = () => {
    if (isEditMode) {
      return (
        <div className="h-full bg-gray-800 rounded-lg overflow-hidden">
          <div className="h-full p-4">
            <textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="w-full h-full bg-gray-900 text-gray-100 p-4 rounded-lg border border-gray-600 focus:border-primary focus:outline-none resize-none font-mono leading-relaxed"
              style={{ fontSize: `${Math.max(state.zoom * 0.01 * 16, 14)}px` }}
              placeholder="Enter markdown content..."
            />
          </div>
        </div>
      )
    }

    return (
      <div className="h-full bg-gray-800 rounded-lg overflow-hidden">
        <div className="h-full p-6 overflow-auto">
          <div
            className="prose prose-invert max-w-none leading-relaxed"
            style={{ fontSize: `${Math.max(state.zoom * 0.01 * 16, 14)}px` }}
            dangerouslySetInnerHTML={{ __html: renderMarkdownToHTML(fileContent) }}
          />
        </div>
      </div>
    )
  }

  const renderMermaidContent = () => (
    <div className="h-full bg-gray-800 rounded-lg overflow-hidden">
      <div className="h-full p-4 overflow-auto">
        <div className="w-full h-full flex items-center justify-center">
          <div
            className="bg-white rounded-lg p-6 max-w-full max-h-full overflow-auto"
            style={{ transform: `scale(${state.zoom / 100})`, transformOrigin: 'center' }}
          >
            <MermaidRenderer content={fileContent} />
          </div>
        </div>
      </div>
    </div>
  )

  const renderTextContent = () => {
    if (isEditMode) {
      return (
        <div className="h-full bg-gray-800 rounded-lg overflow-hidden">
          <div className="h-full p-4">
            <textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="w-full h-full bg-gray-900 text-gray-100 p-4 rounded-lg border border-gray-600 focus:border-primary focus:outline-none resize-none font-mono leading-relaxed"
              style={{ fontSize: `${Math.max(state.zoom * 0.01 * 16, 14)}px` }}
              placeholder="Enter text content..."
            />
          </div>
        </div>
      )
    }

    return (
      <div className="h-full bg-gray-800 rounded-lg overflow-hidden">
        <div className="h-full p-4 overflow-auto">
          <pre
            className="whitespace-pre-wrap font-mono text-gray-100 leading-relaxed"
            style={{ fontSize: `${Math.max(state.zoom * 0.01 * 16, 14)}px` }}
          >
            {fileContent}
          </pre>
        </div>
      </div>
    )
  }

  const renderImageContent = () => (
    <div className="h-full bg-gray-800 rounded-lg overflow-hidden flex items-center justify-center">
      <div className="max-w-full max-h-full p-4">
        <img
          src={`data:image/${fileTypeInfo?.extension === 'jpg' ? 'jpeg' : fileTypeInfo?.extension};base64,${fileContent}`}
          alt={state.fileName || 'Image'}
          className="max-w-full max-h-full object-contain rounded shadow-lg"
          style={{ transform: `scale(${state.zoom / 100})` }}
        />
      </div>
    </div>
  )

  const renderUnsupportedContent = () => (
    <div className="h-full bg-gray-800 rounded-lg flex items-center justify-center">
      <div className="text-center text-gray-400">
        <FontAwesomeIcon icon={ICONS.file} className="text-4xl mb-4" />
        <p className="text-lg font-medium">Unsupported File Type</p>
        <p className="text-sm">Cannot preview .{fileTypeInfo?.extension} files</p>
      </div>
    </div>
  )

  // Simple markdown to HTML renderer
  const renderMarkdownToHTML = (markdown: string): string => {
    let html = markdown

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold text-white mt-6 mb-3">$1</h3>')
    html = html.replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold text-white mt-8 mb-4">$1</h2>')
    html = html.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-white mt-8 mb-6">$1</h1>')

    // Bold and italic
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-white">$1</strong>')
    html = html.replace(/\*(.*?)\*/g, '<em class="italic text-gray-200">$1</em>')

    // Code blocks
    html = html.replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-700 p-4 rounded-lg my-4 overflow-x-auto"><code class="text-sm font-mono text-gray-100">$1</code></pre>')

    // Inline code
    html = html.replace(/`(.*?)`/g, '<code class="bg-gray-700 px-2 py-1 rounded text-sm font-mono text-gray-100">$1</code>')

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary hover:text-primary/80 underline" target="_blank" rel="noopener noreferrer">$1</a>')

    // Lists
    html = html.replace(/^\* (.*$)/gim, '<li class="text-gray-200 mb-1">$1</li>')
    html = html.replace(/^- (.*$)/gim, '<li class="text-gray-200 mb-1">$1</li>')

    // Wrap lists
    html = html.replace(/(<li.*<\/li>)/gs, '<ul class="list-disc list-inside space-y-1 my-4 text-gray-200">$1</ul>')

    // Paragraphs
    html = html.replace(/\n\n/g, '</p><p class="text-gray-200 mb-4">')
    html = '<p class="text-gray-200 mb-4">' + html + '</p>'

    // Clean up empty paragraphs
    html = html.replace(/<p class="text-gray-200 mb-4"><\/p>/g, '')

    return html
  }

  const handleNextPage = async () => {
    if (pdfDocument && currentPage < totalPages) {
      const nextPage = currentPage + 1
      setCurrentPage(nextPage)
      await renderPage(pdfDocument, nextPage)
    }
  }

  const handlePrevPage = async () => {
    if (pdfDocument && currentPage > 1) {
      const prevPage = currentPage - 1
      setCurrentPage(prevPage)
      await renderPage(pdfDocument, prevPage)
    }
  }

  if (!state.isOpen) return null

  return (
    <div className="absolute inset-0 bg-gray-900 z-50 flex">
      
      {/* Center Column - PDF Viewer */}
      <div className="flex-1 bg-gray-800 flex flex-col border-r border-tertiary/50">
        
        {/* File Viewer Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button onClick={handleClose} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.arrowLeft} className="text-gray-400 text-sm" />
            </button>
            <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-lg" />
            <span className="text-supplement1 font-semibold text-lg">{state.fileName || fileTypeInfo?.displayName || 'Document'}</span>
          </div>
          <div className="flex items-center gap-2">
            <button onClick={handleZoomOut} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.searchMinus} className="text-gray-400 text-sm" />
            </button>
            <span className="text-gray-400 text-sm">{Math.round(fileTypeInfo?.type === 'pdf' ? pdfState.zoom : state.zoom)}%</span>
            <button onClick={handleZoomIn} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.searchPlus} className="text-gray-400 text-sm" />
            </button>
            <button onClick={resetZoom} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.refresh} className="text-gray-400 text-sm" />
            </button>
            <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.download} className="text-gray-400 text-sm" />
            </button>
            {supportsEdit(state.fileName) && (
              <>
                <button
                  onClick={toggleEditMode}
                  className={`p-2 hover:bg-gray-700 rounded-lg transition-colors ${isEditMode ? 'bg-primary/20 text-primary' : ''}`}
                  title={isEditMode ? 'Exit Edit Mode' : 'Edit File'}
                >
                  <FontAwesomeIcon icon={ICONS.penToSquare} className="text-sm" />
                </button>
                {isEditMode && (
                  <button
                    onClick={saveFile}
                    className="p-2 hover:bg-gray-700 rounded-lg transition-colors text-green-400"
                    title="Save File"
                  >
                    <FontAwesomeIcon icon={ICONS.floppyDisk} className="text-sm" />
                  </button>
                )}
              </>
            )}
          </div>
        </div>
        
        {/* File Content */}
        <div className="flex-1 overflow-hidden bg-gray-900 p-4">
          {state.isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-400">
                <FontAwesomeIcon icon={ICONS.spinner} className="text-4xl mb-4 animate-spin" />
                <p className="text-lg font-medium">Loading {fileTypeInfo?.displayName || 'File'}...</p>
                <p className="text-sm">Please wait while we load the document</p>
              </div>
            </div>
          ) : state.error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-red-400">
                <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-4xl mb-4" />
                <p className="text-lg font-medium">Failed to load file</p>
                <p className="text-sm">{state.error}</p>
                <button
                  onClick={() => fileTypeInfo && loadFileContent(fileTypeInfo)}
                  className="mt-4 px-4 py-2 bg-secondary hover:bg-secondary/80 text-gray-900 rounded-lg transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : fileTypeInfo && (fileContent || pdfDocument) ? (
            renderFileContent()
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-400">
                <FontAwesomeIcon icon={getFileIcon()} className="text-4xl mb-4" />
                <p className="text-lg font-medium">No file loaded</p>
                <p className="text-sm">Select a file to view</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Column - File Details Panel */}
      <div className="w-1/3 bg-gray-800 flex flex-col overflow-hidden min-w-[320px] max-w-[480px]">
        
        {/* File Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-lg flex-shrink-0" />
            <span className="text-supplement1 font-semibold text-sm truncate">{state.fileName || fileTypeInfo?.displayName || 'Document'}</span>
          </div>
          <div className="flex items-center gap-1 ml-2">
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.lock} className="text-red-500 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                Private Document
              </div>
            </button>
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.ellipsisVertical} className="text-gray-400 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                System Operations
              </div>
            </button>
            <button onClick={handleClose} className="p-1 hover:bg-gray-700 rounded transition-colors">
              <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>
        
        {/* Tags Section - Placeholder for now */}
        <div className="p-3 border-b border-tertiary/50">
          <p className="text-xs text-gray-400 mb-2">Select any key ideas about this doc to enhance the AI context learning.</p>
          <div className="flex flex-wrap gap-1 mb-2">
            <span className="px-2 py-0.5 bg-primary/20 text-primary text-xs rounded-full border border-primary/30 font-medium">{fileTypeInfo?.displayName || 'Document'}</span>
            <button className="px-2 py-0.5 border border-dashed border-gray-600 text-gray-400 text-xs rounded-full hover:border-gray-500 transition-colors">
              <FontAwesomeIcon icon={ICONS.plus} className="mr-1" />Add
            </button>
          </div>
        </div>
        
        {/* Document Summary - Placeholder for now */}
        <div className="p-3 border-b border-tertiary/50 flex-1 overflow-y-auto">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-supplement1 font-semibold text-sm">Document Info</h3>
          </div>
          <div className="text-xs text-gray-400 space-y-2">
            <p><span className="text-supplement1">File:</span> {state.fileName || 'Unknown'}</p>
            <p><span className="text-supplement1">Type:</span> {fileTypeInfo?.displayName || 'Unknown'}</p>
            {fileTypeInfo?.type === 'pdf' && (
              <>
                <p><span className="text-supplement1">Pages:</span> {totalPages || 'Loading...'}</p>
                <p><span className="text-supplement1">Current Page:</span> {currentPage}</p>
              </>
            )}
            {fileTypeInfo?.type === 'text' || fileTypeInfo?.type === 'code' || fileTypeInfo?.type === 'markdown' ? (
              <p><span className="text-supplement1">Lines:</span> {fileContent.split('\n').length}</p>
            ) : null}
            <p><span className="text-supplement1">Zoom:</span> {Math.round(fileTypeInfo?.type === 'pdf' ? pdfState.zoom : state.zoom)}%</p>
          </div>
        </div>
        
        {/* Action Buttons - Placeholder for now */}
        <div className="p-4 border-t border-tertiary/50">
          <div className="flex gap-2">
            <button className="flex-1 px-3 py-2 bg-primary hover:bg-primary/80 text-gray-900 rounded-lg transition-colors text-sm font-medium">
              Chat with Document
            </button>
            <button className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-supplement1 rounded-lg transition-colors text-sm">
              <FontAwesomeIcon icon={ICONS.share} />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
