import React, { useEffect, useRef, useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'

interface FileContextMenuProps {
  isOpen: boolean
  position: { x: number; y: number }
  selectedFile?: string
  filePath?: string
  selectedContextId?: string
  onClose: () => void
  onAction: (action: string, data?: any) => void
}

interface ContextMenuItem {
  id: string
  label: string
  icon: any
  shortcut?: string
  disabled?: boolean
  destructive?: boolean
  separator?: boolean
  submenu?: ContextMenuItem[]
  onClick?: () => void
}

export const FileContextMenu: React.FC<FileContextMenuProps> = ({
  isOpen,
  position,
  selectedFile,
  filePath,
  selectedContextId,
  onClose,
  onAction
}) => {
  const menuRef = useRef<HTMLDivElement>(null)
  const [submenuOpen, setSubmenuOpen] = useState<string | null>(null)

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  // Adjust menu position to stay within viewport
  useEffect(() => {
    if (isOpen && menuRef.current) {
      const menu = menuRef.current
      const rect = menu.getBoundingClientRect()
      const viewport = {
        width: window.innerWidth,
        height: window.innerHeight
      }

      let adjustedX = position.x
      let adjustedY = position.y

      // Adjust horizontal position
      if (position.x + rect.width > viewport.width) {
        adjustedX = viewport.width - rect.width - 10
      }

      // Adjust vertical position
      if (position.y + rect.height > viewport.height) {
        adjustedY = viewport.height - rect.height - 10
      }

      menu.style.left = `${Math.max(10, adjustedX)}px`
      menu.style.top = `${Math.max(10, adjustedY)}px`
    }
  }, [isOpen, position])

  const handleAction = (actionId: string, data?: any) => {
    onAction(actionId, { file: selectedFile, path: filePath, ...data })
    onClose()
  }

  // Helper function to check if file supports preview
  const supportsPreview = (fileName?: string): boolean => {
    if (!fileName) return false
    const ext = fileName.split('.').pop()?.toLowerCase()
    const previewExtensions = [
      'pdf', 'md', 'markdown', 'txt', 'log', 'csv', 'xml', 'json', 'yaml', 'yml',
      'js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt',
      'jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp', 'ico'
    ]
    return ext ? previewExtensions.includes(ext) : false
  }

  // Helper function to check if file supports editing
  const supportsEdit = (fileName?: string): boolean => {
    if (!fileName) return false
    const ext = fileName.split('.').pop()?.toLowerCase()
    const editableExtensions = [
      'md', 'markdown', 'txt', 'log', 'csv', 'xml', 'json', 'yaml', 'yml',
      'js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'
    ]
    return ext ? editableExtensions.includes(ext) : false
  }

  const menuItems: ContextMenuItem[] = [
    {
      id: 'preview',
      label: 'Preview',
      icon: ICONS.eye,
      shortcut: 'Space',
      disabled: !supportsPreview(selectedFile),
      onClick: () => handleAction('preview')
    },
    {
      id: 'edit',
      label: 'Edit',
      icon: ICONS.penToSquare,
      shortcut: 'F4',
      disabled: !supportsEdit(selectedFile),
      onClick: () => handleAction('edit')
    },
    { id: 'separator-0', label: '', icon: null, separator: true },
    {
      id: 'open',
      label: 'Open',
      icon: ICONS.folderOpen,
      shortcut: 'Enter',
      onClick: () => handleAction('open')
    },
    {
      id: 'open-external',
      label: 'Open with System App',
      icon: ICONS.externalLink,
      shortcut: 'Ctrl+O',
      onClick: () => handleAction('open-external')
    },
    { id: 'separator-1', label: '', icon: null, separator: true },
    {
      id: 'copy-path',
      label: 'Copy Path',
      icon: ICONS.clipboard,
      shortcut: 'Ctrl+Shift+C',
      onClick: () => handleAction('copy-path')
    },
    {
      id: 'show-in-explorer',
      label: 'Show in Explorer',
      icon: ICONS.folderOpen,
      shortcut: 'Ctrl+Shift+R',
      onClick: () => handleAction('show-in-explorer')
    },
    { id: 'separator-1.5', label: '', icon: null, separator: true },
    {
      id: 'copy',
      label: 'Copy',
      icon: ICONS.copy,
      shortcut: 'Ctrl+C',
      onClick: () => handleAction('copy')
    },
    {
      id: 'cut',
      label: 'Cut',
      icon: ICONS.cut,
      shortcut: 'Ctrl+X',
      onClick: () => handleAction('cut')
    },
    {
      id: 'duplicate',
      label: 'Duplicate',
      icon: ICONS.clone,
      shortcut: 'Ctrl+D',
      onClick: () => handleAction('duplicate')
    },
    { id: 'separator-2', label: '', icon: null, separator: true },
    {
      id: 'ai-actions',
      label: 'AI Actions',
      icon: ICONS.brain,
      submenu: [
        {
          id: 'summarize-document',
          label: 'Summarize Document',
          icon: ICONS.fileText,
          onClick: () => handleAction('summarize-document')
        },
        {
          id: 'extract-key-points',
          label: 'Extract Key Points',
          icon: ICONS.lightbulb,
          onClick: () => handleAction('extract-key-points')
        },
        {
          id: 'create-chat-context',
          label: 'Create Chat Context',
          icon: ICONS.comments,
          onClick: () => handleAction('create-chat-context')
        },
        {
          id: 'generate-questions',
          label: 'Generate Questions',
          icon: ICONS.magic,
          onClick: () => handleAction('generate-questions')
        }
      ]
    },
    {
      id: 'vault-actions',
      label: 'Vault Actions',
      icon: ICONS.cube,
      submenu: [
        {
          id: 'move-to-vault',
          label: 'Move to Vault...',
          icon: ICONS.arrowRight,
          onClick: () => handleAction('move-to-vault')
        },
        {
          id: 'copy-to-vault',
          label: 'Copy to Vault...',
          icon: ICONS.copy,
          onClick: () => handleAction('copy-to-vault')
        },
        {
          id: 'add-to-master',
          label: 'Add to Master.md',
          icon: ICONS.plus,
          onClick: () => handleAction('add-to-master')
        }
      ]
    },
    { id: 'separator-3', label: '', icon: null, separator: true },
    {
      id: 'rename',
      label: 'Rename',
      icon: ICONS.edit,
      shortcut: 'F2',
      onClick: () => handleAction('rename')
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: ICONS.trash,
      shortcut: 'Del',
      destructive: true,
      onClick: () => handleAction('delete')
    },
    { id: 'separator-4', label: '', icon: null, separator: true },
    {
      id: 'properties',
      label: 'Properties',
      icon: ICONS.info,
      shortcut: 'Alt+Enter',
      onClick: () => handleAction('properties')
    }
  ]

  const renderMenuItem = (item: ContextMenuItem, isSubmenu = false) => {
    if (item.separator) {
      return <div key={item.id} className="border-t border-gray-600 my-1" />
    }

    const hasSubmenu = item.submenu && item.submenu.length > 0
    const isSubmenuOpen = submenuOpen === item.id

    return (
      <div key={item.id} className="relative">
        <button
          className={`
            w-full flex items-center justify-between px-3 py-2 text-left text-sm transition-colors
            ${item.disabled 
              ? 'text-gray-500 cursor-not-allowed' 
              : item.destructive
                ? 'text-secondary hover:bg-red-900/20'
                : 'text-supplement1 hover:bg-gray-700'
            }
          `}
          disabled={item.disabled}
          onClick={hasSubmenu ? () => setSubmenuOpen(isSubmenuOpen ? null : item.id) : item.onClick}
          onMouseEnter={() => hasSubmenu && setSubmenuOpen(item.id)}
        >
          <div className="flex items-center gap-3">
            <FontAwesomeIcon 
              icon={item.icon} 
              className={`text-sm ${item.destructive ? 'text-secondary' : 'text-primary'}`} 
            />
            <span>{item.label}</span>
          </div>
          <div className="flex items-center gap-2">
            {item.shortcut && (
              <span className="text-xs text-gray-400">{item.shortcut}</span>
            )}
            {hasSubmenu && (
              <FontAwesomeIcon icon={ICONS.chevronRight} className="text-xs text-gray-400" />
            )}
          </div>
        </button>

        {/* Submenu */}
        {hasSubmenu && isSubmenuOpen && (
          <div className="absolute left-full top-0 ml-1 bg-gray-800 border border-gray-600 rounded-lg shadow-xl min-w-48 z-50">
            {item.submenu!.map(subItem => renderMenuItem(subItem, true))}
          </div>
        )}
      </div>
    )
  }

  if (!isOpen || !selectedFile) {
    return null
  }

  return (
    <div
      ref={menuRef}
      className="fixed bg-gray-800 border border-gray-600 rounded-lg shadow-xl min-w-56 z-50 py-1"
      style={{ left: position.x, top: position.y }}
    >
      {/* Header */}
      <div className="px-3 py-2 border-b border-gray-600">
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={ICONS.file} className="text-primary text-sm" />
          <span className="text-sm font-medium text-supplement1 truncate" title={selectedFile}>
            {selectedFile}
          </span>
        </div>
      </div>

      {/* Menu Items */}
      <div className="py-1">
        {menuItems.map(item => renderMenuItem(item))}
      </div>
    </div>
  )
}
